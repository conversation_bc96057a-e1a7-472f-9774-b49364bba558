# RealMaster 开发环境 Nginx配置
# 基于实际的goupload配置提供静态文件服务

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;
    client_max_body_size 2048m;  # 支持大文件访问

    server {
        listen 3000;
        server_name localhost;

        # 跨域支持
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Range, Content-Type" always;

        # 草稿视频文件
        location /draft/videos/ {
            alias /var/www/draft/rm_video_drafts/;
            add_header Accept-Ranges bytes;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type" always;
        }

        # 草稿缩略图文件
        location /draft/thumbnails/ {
            alias /var/www/draft/rm_thumbnail_drafts/;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type" always;
        }

        # 最终视频文件 - 支持HLS流和普通视频
        location /media/videos/ {
            alias /var/www/media/rm_videos/;
            add_header Accept-Ranges bytes;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type" always;

            # HLS流文件特殊处理
            location ~* \.(m3u8|ts)$ {
                add_header Cache-Control "no-cache" always;
                add_header Access-Control-Allow-Origin "*" always;
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
                add_header Access-Control-Allow-Headers "Range, Content-Type" always;
            }
        }

        # 最终缩略图文件
        location /media/thumbnails/ {
            alias /var/www/media/rm_thumbnails/;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type" always;
            expires 1h;
        }

        # 客户头像文件
        location /media/avatars/ {
            alias /var/www/media/rm_avatars/;
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS" always;
            add_header Access-Control-Allow-Headers "Range, Content-Type" always;
            expires 1h;
        }

        # 处理OPTIONS预检请求
        location / {
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, HEAD, OPTIONS";
                add_header Access-Control-Allow-Headers "Range, Content-Type";
                add_header Content-Length 0;
                add_header Content-Type text/plain;
                return 204;
            }
            return 404;
        }
    }
}
