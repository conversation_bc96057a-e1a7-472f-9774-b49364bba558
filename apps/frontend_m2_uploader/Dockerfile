# This Dockerfile now expects a pre-built 'dist' directory in the build context.
# Please run `npm run build` locally before building the image.
FROM nginx:1.27-alpine

# Set the working directory inside the container
WORKDIR /usr/share/nginx/html

# Clean any default Nginx content to ensure a fresh start
RUN rm -rf ./*

# Copy the contents of the pre-built local 'dist' directory into the image
COPY ./dist/ .

# Expose port 80 to allow incoming HTTP traffic
EXPOSE 80

# Command to start the Nginx server in the foreground
CMD ["nginx", "-g", "daemon off;"] 