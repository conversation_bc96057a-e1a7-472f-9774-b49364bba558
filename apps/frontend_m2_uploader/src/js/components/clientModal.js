import { createClient, updateClient } from '../services/apiClient';
import { invalidateDropdownCache } from './customDropdown';

/**
 * A reusable module for the client creation/editing modal.
 */

let selectedAvatarFile = null;
let onSuccessCallback = null;

// Cache DOM elements to avoid repeated queries
let modal, form, saveBtn, cancelBtn, uploader, uploaderInput, preview, uploadArea, removeBtn, previewModal, fullscreenImage, closePreviewBtn;

/**
 * Initializes the modal's DOM elements and attaches event listeners.
 * This should be called once when the page containing the modal is loaded.
 */
export const initClientModal = () => {
    modal = document.getElementById('client-modal');
    if (!modal) return; // Don't proceed if the modal isn't on the current page

    form = document.getElementById('client-modal-form');
    saveBtn = document.getElementById('save-client-btn');
    cancelBtn = document.getElementById('cancel-client-btn');
    uploader = document.getElementById('avatar-uploader');
    uploaderInput = document.getElementById('avatar-upload-input');
    preview = document.getElementById('avatar-preview');
    uploadArea = uploader.querySelector('.upload-area');
    removeBtn = document.getElementById('remove-avatar-btn');
    previewModal = document.getElementById('image-preview-modal');
    fullscreenImage = document.getElementById('fullscreen-image');
    closePreviewBtn = document.getElementById('close-preview-btn');
    
    // Attach all event listeners
    cancelBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (e) => (e.target === modal) && closeModal());
    
    uploader.addEventListener('click', (e) => {
        if (e.target.closest('#remove-avatar-btn')) return;
        uploaderInput.click();
    });

    removeBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        resetUploader();
    });

    uploaderInput.addEventListener('change', handleFileSelect);
    preview.addEventListener('click', handlePreviewClick);
    closePreviewBtn.addEventListener('click', closePreviewModal);
    previewModal.addEventListener('click', (e) => (e.target === previewModal) && closePreviewModal());

    form.addEventListener('input', checkFormValidity);
    saveBtn.addEventListener('click', handleSave);
};

/**
 * Opens the client modal for creating or editing.
 * @param {object | null} clientData - The client data for editing, or null for creating.
 * @param {function} onSuccess - The callback function to execute after a successful save.
 */
export const openClientModal = (clientData, onSuccess) => {
    onSuccessCallback = onSuccess;
    form.reset();
    resetUploader();
    
    if (clientData) {
        // Edit mode
        document.getElementById('client-modal-title').textContent = 'Edit Client';
        document.getElementById('client-id').value = clientData.id;
        document.getElementById('client-name').value = clientData.name;
        document.getElementById('client-email').value = clientData.email;
        document.getElementById('client-phone').value = clientData.phone;
        document.getElementById('client-memo').value = clientData.memo;
        if (clientData.avatarUrl && !clientData.avatarUrl.endsWith('head.svg')) {
            preview.src = clientData.avatarUrl;
            preview.classList.remove('hidden');
            uploadArea.classList.add('has-image');
            removeBtn.classList.remove('hidden');
        }
    } else {
        // Create mode
        document.getElementById('client-modal-title').textContent = 'New Client';
        document.getElementById('client-id').value = '';
    }
    
    checkFormValidity();
    modal.classList.remove('hidden');
};

function closeModal() {
    modal.classList.add('hidden');
    selectedAvatarFile = null;
    onSuccessCallback = null;
}

function resetUploader() {
    preview.src = '';
    preview.classList.add('hidden');
    uploadArea.classList.remove('has-image');
    uploaderInput.value = '';
    selectedAvatarFile = null;
    removeBtn.classList.add('hidden');
    checkFormValidity();
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
            preview.src = e.target.result;
            preview.classList.remove('hidden');
            uploadArea.classList.add('has-image');
            removeBtn.classList.remove('hidden');
        };
        reader.readAsDataURL(file);
        selectedAvatarFile = file;
    } else {
        resetUploader();
    }
    checkFormValidity();
}

function handlePreviewClick(event) {
    event.stopPropagation();
    if (preview.src && !preview.classList.contains('hidden')) {
        fullscreenImage.src = preview.src;
        previewModal.classList.remove('hidden');
    }
}

function closePreviewModal() {
    previewModal.classList.add('hidden');
}

function checkFormValidity() {
    const isEditMode = !!form.querySelector('#client-id').value;
    const isNameValid = form.querySelector('#client-name').value.trim() !== '';
    const isEmailValid = form.querySelector('#client-email').value.trim() !== '';
    const isPhoneValid = form.querySelector('#client-phone').value.trim() !== '';

    if (isEditMode) {
        saveBtn.disabled = !(isNameValid && isEmailValid && isPhoneValid);
    } else {
        const isAvatarSelected = !!selectedAvatarFile;
        saveBtn.disabled = !(isNameValid && isEmailValid && isPhoneValid && isAvatarSelected);
    }
}

async function handleSave() {
    saveBtn.disabled = true;
    const clientId = document.getElementById('client-id').value;
    const formData = new FormData();

    formData.append('nm', document.getElementById('client-name').value.trim());
    formData.append('em', document.getElementById('client-email').value.trim());
    formData.append('ph', document.getElementById('client-phone').value.trim());
    formData.append('rem', document.getElementById('client-memo').value.trim());
    formData.append('mUid', document.getElementById('client-rm-id').value.trim());
    
    if (selectedAvatarFile) {
        formData.append('avatar', selectedAvatarFile);
    }

    try {
        let result;
        if (clientId) {
            result = await updateClient(clientId, formData);
        } else {
            result = await createClient(formData);
        }

        if (onSuccessCallback) {
            // Pass the newly created/updated client data to the callback
            onSuccessCallback(result.data);
        }

        // 触发客户缓存失效事件
        invalidateDropdownCache('clients-changed');

        closeModal();
    } catch (error) {
        alert(`Failed to save client: ${error.message}`);
        checkFormValidity(); // Re-enable save button
    }
} 