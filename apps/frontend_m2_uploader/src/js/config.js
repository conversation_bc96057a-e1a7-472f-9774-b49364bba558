// Centralized configuration for the application.

// The base URL for all API requests.
// Using VITE_API_BASE_URL environment variable if available, otherwise fallback to local dev server.
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL ?? 'http://localhost:8080';

// The base URL for media files (served by nginx).
// 媒体文件通过nginx提供，端口3000
export const MEDIA_BASE_URL = import.meta.env.VITE_MEDIA_BASE_URL ?? 'http://localhost:3000';

// Other configurations can be added here.
export default {
    API_BASE_URL,
    MEDIA_BASE_URL,
};