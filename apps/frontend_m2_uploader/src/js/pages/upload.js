import { renderView } from '../viewRenderer';
import { getCategoryList, getClientList, searchMlsProperties, createVideo, createVideoFromChunkedUpload, uploadThumbnail } from '../services/apiClient';
import { navigateTo, setPageCleanup } from '../router';
import { setupCustomDropdown } from '../components/customDropdown';
import { setupFileUploader } from '../components/fileUploader';
import { initClientModal, openClientModal } from '../components/clientModal';

const showPreviewModal = (url, fileType) => {
    const modal = document.getElementById('preview-modal');
    const previewContent = document.getElementById('preview-content');
    if (!modal || !previewContent) return;

    previewContent.innerHTML = ''; // Clear previous content

    let mediaElement;
    if (fileType === 'video') {
        mediaElement = document.createElement('video');
        mediaElement.controls = true;
        mediaElement.autoplay = true;
    } else {
        mediaElement = document.createElement('img');
    }
    mediaElement.src = url;

    previewContent.appendChild(mediaElement);
    modal.classList.remove('hidden');
};

const setupModalControls = () => {
    const modal = document.getElementById('preview-modal');
    const closeBtn = document.getElementById('close-preview-btn');
    const previewContent = document.getElementById('preview-content');

    if (!modal || !closeBtn || !previewContent) return;

    const closeModal = () => {
        modal.classList.add('hidden');
        // Stop video playback by removing the element's src or the element itself
        previewContent.innerHTML = '';
    };

    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', (event) => {
        // Close if clicked on the overlay, not the content
        if (event.target === modal) {
            closeModal();
        }
    });
};

// --- Translation Helper ---
const setupTranslation = () => {
    const translateButtons = document.querySelectorAll('.btn-translate');
    
    translateButtons.forEach(button => {
        button.addEventListener('click', async (e) => {
            const container = e.target.closest('.input-with-btn');
            if (!container) return;

            const sourceInput = container.querySelector('input');
            const targetInput = container.nextElementSibling;

            if (!sourceInput || !targetInput) return;

            const sourceText = sourceInput.value.trim();
            if (!sourceText) return;

            // Provide user feedback
            const originalButtonText = e.target.textContent;
            e.target.textContent = '...';
            e.target.disabled = true;

            try {
                const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(sourceText)}&langpair=zh-CN|en-GB`);
                const data = await response.json();
                
                if (data && data.responseData && data.responseData.translatedText) {
                    targetInput.value = data.responseData.translatedText;
                    // Trigger input event for form validation
                    targetInput.dispatchEvent(new Event('input'));
                } else {
                    alert('Translation failed. Please try again.');
                }
            } catch (error) {
                console.error('Translation API error:', error);
                alert('Translation request failed. Check the console for details.');
            } finally {
                // Restore button
                e.target.textContent = originalButtonText;
                e.target.disabled = false;
            }
        });
    });
};

// --- New MLS ID Component Logic ---

// Debounce utility to limit API calls
const debounce = (func, delay) => {
    let timeout;
    return function(...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
};

const setupMlsIdComponent = () => {
    const container = document.getElementById('mls-id-container');
    const template = document.getElementById('mls-id-row-template');
    const addButton = document.getElementById('add-mls-id-btn');

    if (!container || !template || !addButton) {
        console.error("MLS ID component elements not found!");
        return;
    }

    const createMlsIdRow = () => {
        const clone = template.content.cloneNode(true);
        const row = clone.querySelector('.mls-id-row');
        const input = clone.querySelector('.mls-id-input');
        const dropdown = clone.querySelector('.search-results-dropdown');
        const resultsList = clone.querySelector('ul');
        const deleteBtn = clone.querySelector('.delete-mls-id-btn');

        const closeDropdown = () => dropdown.classList.remove('open');
        
        const handleSearch = async (event) => {
            const keyword = event.target.value.trim();
            if (keyword.length < 2) { // Only search if 2+ characters
                closeDropdown();
                return;
            }

            const properties = await searchMlsProperties(keyword);
            resultsList.innerHTML = '';

            if (properties.length > 0) {
                properties.forEach(prop => {
                    const li = document.createElement('li');
                    li.dataset.id = prop.id;
                    li.innerHTML = `
                        <div class="result-addr">${prop.searchAddr}</div>
                        <div class="result-meta">${prop.id} &middot; ${prop.city}, ${prop.prov}</div>
                    `;
                    resultsList.appendChild(li);
                });
                dropdown.classList.add('open');
            } else {
                closeDropdown();
            }
        };

        input.addEventListener('input', debounce(handleSearch, 300));

        resultsList.addEventListener('click', (event) => {
            const li = event.target.closest('li');
            if (li) {
                input.value = li.dataset.id;
                closeDropdown();
            }
        });

        deleteBtn.addEventListener('click', () => {
            row.remove();
        });

        // Close dropdown if clicking outside
        document.addEventListener('click', (event) => {
            if (!row.contains(event.target)) {
                closeDropdown();
            }
        });

        return row;
    };

    addButton.addEventListener('click', () => {
        const newRow = createMlsIdRow();
        container.appendChild(newRow);
    });

    // Initial row
    container.appendChild(createMlsIdRow());
};

export const renderUpload = async () => {
    try {
        await renderView('upload', '#app-container');

        const videoUploaderInstance = setupFileUploader('video-uploader', {
            onPreview: (url) => showPreviewModal(url, 'video'),
            onFileChange: () => checkFormValidityAndUpdateButton(),
            onUploadProgress: (percentage, uploadedBytes, totalBytes) => {
                console.log(`上传进度: ${percentage.toFixed(1)}% (${Math.round(uploadedBytes/1024/1024)}MB / ${Math.round(totalBytes/1024/1024)}MB)`);
                // 这里可以更新UI显示上传进度
                updateUploadProgress(percentage);
            }
        });

        // 上传进度显示函数
        const updateUploadProgress = (percentage) => {
            // 可以在这里添加进度条UI更新逻辑
            // 例如：更新进度条、显示百分比等
            console.log(`Upload progress: ${percentage.toFixed(1)}%`);
        };

        const coverUploaderInstance = setupFileUploader('cover-uploader', {
            onPreview: (url) => showPreviewModal(url, 'image'),
            onFileChange: () => checkFormValidityAndUpdateButton(),
        });

        setupModalControls();
        setupTranslation();
        setupMlsIdComponent();

        // Initialize and setup the client dropdown
        const clientDropdown = document.getElementById('client-dropdown');
        if (clientDropdown) {
            const clientData = await getClientList({ limit: 999 }); // Get all clients
            setupCustomDropdown(clientDropdown, clientData.items.map(c => ({ id: c.id, name: c.nm })));
        }

        // Setup the new client button
        const newClientBtn = document.querySelector('.label-row .btn-new');
        if (newClientBtn) {
            newClientBtn.addEventListener('click', () => {
                openClientModal(null, (newClient) => {
                    // This is the onSuccess callback
                    const dropdownElement = document.getElementById('client-dropdown');
                    const optionsList = dropdownElement.querySelector('.options-list');
                    const selectedValue = dropdownElement.querySelector('.selected-value');

                    // 1. Create and add the new option
                    const newOption = document.createElement('li');
                    newOption.dataset.value = newClient.id;
                    newOption.textContent = newClient.nm;
                    optionsList.appendChild(newOption);

                    // 2. Select the new option
                    selectedValue.textContent = newClient.nm;
                    dropdownElement.dataset.value = newClient.id;
                    
                    // Optional: show a success message
                    alert(`New client "${newClient.nm}" created and selected.`);
                });
            });
        }
        
        // Initialize the reusable client modal
        initClientModal();

        const form = document.getElementById('upload-form');
        const titleCnInput = document.getElementById('title-cn');
        const descriptionCnInput = document.getElementById('description-cn');
        const publishBtn = document.getElementById('publish-btn');
        const saveDraftBtn = document.getElementById('save-draft-btn');

        const checkFormValidityAndUpdateButton = () => {
            const videoFile = videoUploaderInstance.getFile();
            const coverFile = coverUploaderInstance.getFile();
            const titleCn = titleCnInput.value.trim();
            const descriptionCn = descriptionCnInput.value.trim();
            
            const isFormValid = videoFile && coverFile && titleCn && descriptionCn;
            publishBtn.disabled = !isFormValid;
            saveDraftBtn.disabled = !isFormValid;
        };

        const handleFormSubmit = async (publishNow) => {
            const videoFile = videoUploaderInstance.getFile();
            const coverFile = coverUploaderInstance.getFile();

            // 构建元数据
            const metadata = {
                title: {
                    zh: document.getElementById('title-cn').value,
                    en: document.getElementById('title-en').value,
                },
                description: {
                    zh: document.getElementById('description-cn').value,
                    en: document.getElementById('description-en').value,
                },
                categoryId: document.getElementById('category-dropdown').dataset.value,
                clientId: document.getElementById('client-dropdown').dataset.value,
                propertyIds: Array.from(document.querySelectorAll('.mls-id-input')).map(input => input.value).filter(Boolean),
                externalUrl: document.getElementById('url-link').value,
                publishNow: publishNow
            };

            try {
                // 检查是否需要使用分块上传
                if (videoFile && videoUploaderInstance.shouldUseChunkedUpload()) {
                    console.log('使用分块上传处理大文件...');
                    await handleChunkedUpload(metadata, videoFile, coverFile);
                } else {
                    console.log('使用传统上传...');
                    await handleTraditionalUpload(metadata, videoFile, coverFile);
                }

                alert('Video processed successfully!');
                navigateTo('/dashboard');
            } catch (error) {
                console.error('Failed to create video:', error);
                alert(`Error: ${error.message}`);
            }
        };

        // 处理传统上传
        const handleTraditionalUpload = async (metadata, videoFile, coverFile) => {
            const formData = new FormData();

            if (videoFile) formData.append('video', videoFile);
            if (coverFile) formData.append('thumbnail', coverFile);

            formData.append('metadata', JSON.stringify(metadata));
            formData.append('publishNow', metadata.publishNow.toString());

            return await createVideo(formData);
        };

        // 处理分块上传
        const handleChunkedUpload = async (metadata, videoFile, coverFile) => {
            // 1. 如果有封面图，先单独上传封面图
            let coverUploadResult = null;
            if (coverFile) {
                console.log('开始上传封面图...');
                try {
                    coverUploadResult = await uploadThumbnail(coverFile);
                    console.log('封面图上传成功:', coverUploadResult);
                } catch (error) {
                    console.error('封面图上传失败:', error);
                    throw new Error('封面图上传失败: ' + error.message);
                }
            }

            // 2. 分块上传视频文件
            console.log('开始分块上传视频文件...');
            const videoUploadResult = await videoUploaderInstance.startChunkedUpload();
            console.log('视频分块上传成功:', videoUploadResult);

            // 3. 使用两个上传结果创建视频记录（纯JSON请求）
            const videoMetadata = {
                ...metadata,
                draftVideoGouploadPath: videoUploadResult.path,
                draftThumbGouploadPath: coverUploadResult ? coverUploadResult.path : undefined
            };

            // 发送JSON请求创建视频记录
            return await createVideoFromChunkedUpload(videoMetadata);
        };

        // Attach event listeners
        form.addEventListener('input', checkFormValidityAndUpdateButton);
        publishBtn.addEventListener('click', () => handleFormSubmit(true));
        saveDraftBtn.addEventListener('click', () => handleFormSubmit(false));
        
        // Initial check
        checkFormValidityAndUpdateButton();


        // Setup dropdowns
        setupCustomDropdown(
            'category-dropdown',
            async () => getCategoryList({ limit: 100 }),
            { addNoneOption: false }
        );

        setupCustomDropdown(
            'client-dropdown',
            async () => getClientList({ limit: 100 }),
            { addNoneOption: true }
        );

        // 设置页面清理函数
        setPageCleanup(() => {
            if (videoUploaderInstance && videoUploaderInstance.cleanup) {
                videoUploaderInstance.cleanup();
            }
            if (coverUploaderInstance && coverUploaderInstance.cleanup) {
                coverUploaderInstance.cleanup();
            }
        });

    } catch (error) {
        console.error(`Error rendering upload page: ${error}`);
        const appContainer = document.getElementById('app-container');
        if (appContainer) {
            appContainer.innerHTML = `<div class="error-message">Error rendering page: ${error.message}</div>`;
        }
    }
};