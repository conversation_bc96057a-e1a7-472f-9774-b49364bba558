import Mustache from 'mustache';

/**
 * 加载HTML模板
 * @param {string} templatePath - 模板文件路径
 * @returns {Promise<string>} 模板内容
 */
const loadTemplate = async (templatePath) => {
    try {
        const response = await fetch(templatePath);
        if (!response.ok) {
            throw new Error(`Failed to load template: ${response.statusText}`);
        }
        return await response.text();
    } catch (error) {
        console.error('Template loading error:', error);
        throw new Error(`Failed to load template: ${error.message}`);
    }
};

/**
 * 渲染视图骨架
 * @param {string} viewName - 视图名称
 * @param {string} targetSelector - 目标DOM选择器
 */
export const renderView = async (viewName, targetSelector) => {
  const targetElement = document.querySelector(targetSelector);
  if (!targetElement) {
    console.error(`Target element not found: ${targetSelector}`);
    return null;
  }

  try {
    // 加载并渲染页面骨架
    const pageTemplatePath = `/templates/${viewName}.html`;
    const pageSkeleton = await loadTemplate(pageTemplatePath);
    targetElement.innerHTML = pageSkeleton;
    
    // 如果需要，运行htmx处理新内容
    if (window.htmx) {
        htmx.process(targetElement);
    }
    
    return targetElement;
  } catch (error) {
    console.error('View rendering error:', error);
    targetElement.innerHTML = `
      <div class="error-message">
        <h3>Page Load Failed</h3>
        <p>${error.message}</p>
        <button onclick="window.location.reload()">Retry</button>
      </div>
    `;
    return null;
  }
};

export default {
  renderView,
}; 