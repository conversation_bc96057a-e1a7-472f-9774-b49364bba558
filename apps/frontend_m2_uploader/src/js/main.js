// Main JavaScript entry point
import '../styles/main.scss';

console.log("M2 Uploader App Initialized");

// 引入HTMX并设置，如果需要全局配置
// import 'htmx.org'; // 如果通过npm安装并希望vite打包


// 后续会在这里初始化全局事件监听器、路由（如果不用HTMX的hx-push-url）等 

// 导入路由初始化函数
import { initializeRouter } from './router';

// HTMX 全局配置
document.addEventListener('htmx:configRequest', (event) => {
  // 添加 CSRF token 到所有请求
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
  if (csrfToken) {
    event.detail.headers['X-CSRF-Token'] = csrfToken;
  }
});

// 添加加载指示器
document.addEventListener('htmx:beforeRequest', (event) => {
  const target = event.detail.target;
  const indicator = target.querySelector('.loading-indicator');
  if (indicator) {
    indicator.style.display = 'flex';
  }
});

document.addEventListener('htmx:afterRequest', (event) => {
  const target = event.detail.target;
  const indicator = target.querySelector('.loading-indicator');
  if (indicator) {
    indicator.style.display = 'none';
  }
});

// 错误处理
document.addEventListener('htmx:responseError', (event) => {
  const error = event.detail.error;
  console.error('HTMX Error:', error);
  
  // 显示错误消息
  const target = event.detail.target;
  const errorMessage = document.createElement('div');
  errorMessage.className = 'error-message';
  errorMessage.textContent = '操作失败，请稍后重试';
  target.appendChild(errorMessage);
  
  // 3秒后移除错误消息
  setTimeout(() => {
    errorMessage.remove();
  }, 3000);
});

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  // 初始化路由
  initializeRouter();
  
  // 可以在这里添加其他初始化代码
  console.log('应用已初始化');
}); 