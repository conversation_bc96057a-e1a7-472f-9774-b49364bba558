@use '../base/variables' as vars;

.category-manager-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

.page-header {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  width: 100%;
  height: 3rem; 
  background-color: #ee0011;
  color: white;
  position: relative;

  .header-title {
    font-size: 1.5rem;
    font-weight: 600;
  }

  .back-btn {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;

    img {
      width: 24px;
      height: 24px;
    }
  }
}

.main-content {
  flex-grow: 1;
  padding: 1.5rem 2rem;
  background-color: white;
  margin: 1.5rem;
  border-radius: 0.5rem;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  min-height: 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  margin-bottom: 1rem;

  .content-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
  }

  .new-category-btn,
  .new-client-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    background-color: #ee0011;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
        background-color: #c5000f;
    }

    img {
      width: 16px;
      height: 16px;
    }
  }
}

.category-list-container {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
}

.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.7rem 0.5rem;
  border-bottom: 1px solid #f1f3f5;

  &:last-child {
    border-bottom: none;
  }

  .category-name {
    font-size: 1rem;
    color: #212529;
  }

  .item-actions {
    display: flex;
    align-items: center;
    gap: 1rem;

    .action-btn {
      background: none;
      border: none;
      cursor: pointer;
      padding: 0.25rem;

      img {
        width: 20px;
        height: 20px;
        opacity: 0.5;
        transition: opacity 0.2s;
      }

      &:hover img {
        opacity: 1;
      }
    }
  }
}

.page-footer {
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1.5rem;

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: #495057;

        .pagination-btn {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1.25rem;
            color: #343a40;
            padding: 0 0.5rem;

            &:disabled {
                color: #adb5bd;
                cursor: not-allowed;
            }
        }
    }
}

// --- Modal Styles ---
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 1;
  transition: opacity 0.3s ease;

  &.hidden {
    opacity: 0;
    pointer-events: none;
  }
}

.modal-content {
  background: white;
  padding: 2.5rem;
  border-radius: 0.75rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;

  .modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 2rem;
  }

  .form-group {
    text-align: left;
    margin-bottom: 2.5rem;

    label {
      display: block;
      margin-bottom: 0.75rem;
      font-weight: 500;
      font-size: 1rem;
    }

    input {
      width: 100%;
      padding: 0.8rem 1rem;
      border: 1px solid #dee2e6;
      border-radius: 0.375rem;
      font-size: 1rem;
      background-color: #f8f9fa;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;

    .btn-secondary, .btn-primary {
      padding: 0.8rem 1rem;
      font-size: 1rem;
      font-weight: 500;
      border-radius: 0.375rem;
      border: 1px solid #ced4da;
      cursor: pointer;
      flex-grow: 1;
      max-width: 200px;
    }

    .btn-secondary {
      background-color: white;
      color: #495057;
    }

    .btn-primary {
      background-color: #339af0; // Blue color from image
      color: white;
      border-color: #339af0;
    }
  }
} 