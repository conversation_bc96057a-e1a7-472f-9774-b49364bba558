@use '../base/variables' as vars;
@use '../components/modal';
@use '../components/custom-dropdown';

// Inherit page layout styles by referencing category manager styles.
// This is a temporary approach to avoid duplication.
// A better long-term solution would be a shared layout component/style.
@use 'category-manager';

.clients-page {
    // Most styles are inherited from .category-manager-page via @import

    .client-list-container {
      flex-grow: 1;
      overflow-y: auto;
      min-height: 0;
    }

    .client-table {
        width: 100%;
        border-collapse: collapse;
        
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: middle;
        }

        thead th {
            font-weight: 600;
            font-size: 0.875rem;
            color: #495057;
            background-color: #f8f9fa;
            position: sticky;
            top: 0;
        }

        tbody tr {
            &:last-child {
                td {
                    border-bottom: none;
                }
            }
        }
        
        .profile-picture {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .memo-cell {
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .item-actions {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 1rem;
        }

        .action-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.25rem;

            img {
                width: 20px;
                height: 20px;
                opacity: 0.5;
            }

             &:hover img {
                opacity: 1;
            }
        }
    }
}

/* Styles for Fullscreen Image Preview Modal */
#image-preview-modal {
    display: flex;
    justify-content: center;
    align-items: center;
    
    &.hidden {
        display: none !important;
    }

    .modal-content {
        max-width: 90vw;
        max-height: 90vh;
        width: auto;
        height: auto;
        border-radius: 8px;
    }

    .close-btn {
        position: absolute;
        top: 20px;
        right: 35px;
        color: #fff;
        font-size: 40px;
        font-weight: bold;
        cursor: pointer;
        transition: color 0.3s;
        
        &:hover {
            color: #ccc;
        }
    }
}

/* Styles for Merge Client Modal */
#merge-client-modal {
    .modal-content {
        /* This will inherit width/max-width from _category-manager's .modal-content */
        text-align: left;
        padding: 2rem;
    }

    .modal-title {
        text-align: center;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
    }
    
    p {
        margin-bottom: 1.5rem;
        line-height: 1.6;
        color: #495057;
        font-size: 0.95rem;
    }

    .form-group {
        margin-bottom: 2rem;
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }
    }
    
    .custom-select {
        width: 100%;
        padding: 0.8rem 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        font-size: 1rem;
        background-color: #f8f9fa;
        -webkit-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 1em;
    }
    
    .modal-actions {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .btn-danger {
        background-color: #d9534f;
        color: white;
        border: 1px solid #d43f3a;

        &:hover {
            background-color: #c9302c;
            border-color: #ac2925;
        }
    }
} 