# RealMaster 视频管理系统 (M2) 全面测试方案

## 📋 测试概述

本测试方案针对 RealMaster 视频管理系统的前端 M2 Uploader 和后端 API 进行全面的人工用户测试，确保系统功能完整性、用户体验和数据一致性。

## 🎯 测试目标

- 验证所有核心功能的正确性
- 确保前后端数据交互的准确性
- 验证用户界面的易用性和响应性
- 检查错误处理和边界情况
- 确保文件上传和媒体处理的稳定性

## 🏗️ 系统架构概览

### 前端 (M2 Uploader)
- **技术栈**: Vite + Vanilla JS + SCSS + Mustache.js + HTMX
- **路由**: Hash-based SPA 路由
- **页面**: Dashboard, Upload, Edit Video, Categories, Clients, Filter
- **API 通信**: Axios-based API client

### 后端 API
- **技术栈**: Go + Gin + MongoDB
- **基础路径**: `/video/admin`
- **主要功能**: 视频管理、分类管理、广告主管理、房源集成、文件上传

## 📝 测试环境准备

### 前置条件
1. 后端服务运行在 `http://127.0.0.1:8080`
2. 前端服务运行在 `http://localhost:5174`
3. MongoDB 数据库正常运行
4. 测试数据已准备（分类、客户、视频样本）

### 测试数据准备
- 创建 3-5 个测试分类
- 创建 3-5 个测试客户/广告主
- 准备测试视频文件（不同格式、大小）
- 准备测试图片文件（头像、封面）

## 🧪 详细测试用例

### 1. 系统初始化和导航测试

#### TC001: 应用启动和首页加载
**测试步骤:**
1. 打开浏览器访问 `http://localhost:5174`
2. 验证页面正常加载
3. 检查导航菜单是否显示
4. 验证默认路由跳转到 Dashboard

**预期结果:**
- 页面无错误加载
- 导航菜单显示所有选项
- 默认显示 Dashboard 页面
- 控制台无错误信息

#### TC002: 页面路由导航
**测试步骤:**
1. 点击导航菜单中的每个选项
2. 验证 URL hash 变化
3. 检查页面内容正确切换
4. 使用浏览器前进/后退按钮

**预期结果:**
- 所有页面正常切换
- URL 正确更新为 `#/page-name`
- 浏览器历史记录正常工作
- 页面内容与路由匹配

### 2. Dashboard 仪表盘测试

#### TC003: Dashboard 数据加载
**测试步骤:**
1. 访问 Dashboard 页面
2. 检查统计数据显示
3. 验证视频列表加载
4. 检查分页功能

**预期结果:**
- 统计卡片显示正确数据（视频数、观看数、点赞数等）
- 视频列表正确显示
- 分页控件正常工作
- 加载状态正确显示

#### TC004: Dashboard 筛选功能
**测试步骤:**
1. 使用状态筛选器（已发布、草稿等）
2. 按分类筛选视频
3. 按客户筛选视频
4. 使用日期范围筛选
5. 组合多个筛选条件

**预期结果:**
- 每个筛选条件正确过滤数据
- 组合筛选正常工作
- 筛选结果实时更新
- URL 参数正确传递

#### TC005: 视频操作功能
**测试步骤:**
1. 点击视频的"编辑"按钮
2. 点击视频的"删除"按钮
3. 确认删除操作
4. 检查视频状态变更

**预期结果:**
- 编辑按钮正确跳转到编辑页面
- 删除确认对话框正常显示
- 删除操作成功执行
- 列表实时更新

### 3. 视频上传测试

#### TC006: 基本视频上传
**测试步骤:**
1. 访问上传页面
2. 选择视频文件（支持格式：MP4, AVI, MOV）
3. 填写视频标题
4. 填写视频描述
5. 选择分类
6. 选择客户/广告主
7. 提交表单

**预期结果:**
- 文件选择器正常工作
- 表单验证正确执行
- 上传进度正确显示
- 成功后跳转到 Dashboard
- 新视频出现在列表中

#### TC007: 文件格式和大小验证
**测试步骤:**
1. 尝试上传不支持的文件格式
2. 上传超大文件
3. 上传空文件
4. 上传损坏的视频文件

**预期结果:**
- 不支持格式显示错误提示
- 大文件显示大小限制提示
- 空文件被拒绝
- 损坏文件显示错误信息

#### TC008: 房源关联功能
**测试步骤:**
1. 在上传表单中搜索房源
2. 输入房源关键词
3. 从搜索结果中选择房源
4. 添加多个房源
5. 删除已添加的房源

**预期结果:**
- 房源搜索正常工作
- 搜索结果正确显示
- 房源选择功能正常
- 多房源管理正确
- 删除操作正常

### 4. 视频编辑测试

#### TC009: 视频信息编辑
**测试步骤:**
1. 从 Dashboard 进入视频编辑页面
2. 修改视频标题
3. 修改视频描述
4. 更改分类
5. 更改客户
6. 保存修改

**预期结果:**
- 编辑页面正确加载视频数据
- 所有字段可正常编辑
- 保存操作成功
- 修改后数据正确更新
- 返回 Dashboard 显示更新后信息

#### TC010: 视频发布功能
**测试步骤:**
1. 编辑草稿状态的视频
2. 点击"发布"按钮
3. 确认发布操作
4. 检查视频状态变更

**预期结果:**
- 发布按钮正确显示
- 发布确认对话框出现
- 视频状态更新为"已发布"
- Dashboard 中状态正确显示

#### TC011: 封面图片管理
**测试步骤:**
1. 在编辑页面上传新封面
2. 预览封面图片
3. 删除现有封面
4. 保存更改

**预期结果:**
- 封面上传功能正常
- 图片预览正确显示
- 删除操作成功
- 保存后封面正确更新

### 5. 分类管理测试

#### TC012: 分类 CRUD 操作
**测试步骤:**
1. 访问分类管理页面
2. 创建新分类
3. 编辑现有分类
4. 删除分类
5. 验证分类排序功能

**预期结果:**
- 分类列表正确显示
- 创建操作成功
- 编辑功能正常
- 删除操作正确执行
- 排序功能正常工作

#### TC013: 分类验证规则
**测试步骤:**
1. 创建重复名称的分类
2. 创建空名称的分类
3. 输入超长描述
4. 测试特殊字符输入

**预期结果:**
- 重复名称被拒绝
- 空名称显示验证错误
- 超长内容被限制或警告
- 特殊字符正确处理

### 6. 客户管理测试

#### TC014: 客户 CRUD 操作
**测试步骤:**
1. 访问客户管理页面
2. 创建新客户
3. 上传客户头像
4. 编辑客户信息
5. 删除客户

**预期结果:**
- 客户列表正确显示
- 创建功能正常
- 头像上传成功
- 编辑操作正确
- 删除功能正常

#### TC015: 客户搜索和筛选
**测试步骤:**
1. 按姓名搜索客户
2. 按电话号码搜索
3. 按邮箱搜索
4. 测试模糊搜索
5. 清空搜索条件

**预期结果:**
- 各种搜索条件正常工作
- 搜索结果准确
- 模糊搜索功能正确
- 清空操作正常

### 7. 筛选页面测试

#### TC016: 高级筛选功能
**测试步骤:**
1. 访问筛选页面
2. 设置多个筛选条件
3. 应用筛选
4. 查看筛选结果
5. 重置筛选条件

**预期结果:**
- 筛选界面正确显示
- 多条件筛选正常工作
- 筛选结果准确
- 重置功能正常

#### TC017: 筛选结果导航
**测试步骤:**
1. 在筛选页面设置条件
2. 点击"查看结果"
3. 验证跳转到 Dashboard
4. 检查筛选条件是否保持

**预期结果:**
- 正确跳转到 Dashboard
- 筛选条件正确传递
- Dashboard 显示筛选后的结果
- URL 参数正确

### 8. 错误处理和边界测试

#### TC018: 网络错误处理
**测试步骤:**
1. 断开网络连接
2. 尝试加载页面数据
3. 尝试提交表单
4. 恢复网络连接
5. 重试操作

**预期结果:**
- 网络错误正确显示
- 用户友好的错误信息
- 重试机制正常工作
- 恢复后功能正常

#### TC019: 服务器错误处理
**测试步骤:**
1. 停止后端服务
2. 尝试各种操作
3. 重启后端服务
4. 验证功能恢复

**预期结果:**
- 服务器错误正确处理
- 错误信息清晰明确
- 不会导致页面崩溃
- 服务恢复后正常工作

#### TC020: 表单验证测试
**测试步骤:**
1. 提交空表单
2. 输入无效数据
3. 测试字段长度限制
4. 测试特殊字符输入

**预期结果:**
- 必填字段验证正确
- 数据格式验证正常
- 长度限制正确执行
- 特殊字符正确处理

### 9. 性能和用户体验测试

#### TC021: 页面加载性能
**测试步骤:**
1. 测量各页面首次加载时间
2. 测试大量数据的加载性能
3. 检查内存使用情况
4. 验证缓存机制

**预期结果:**
- 页面加载时间合理（< 3秒）
- 大数据量不影响性能
- 内存使用稳定
- 缓存正确工作

#### TC022: 响应式设计测试
**测试步骤:**
1. 在不同屏幕尺寸下测试
2. 测试移动设备兼容性
3. 验证触摸操作
4. 检查布局适应性

**预期结果:**
- 各尺寸屏幕正常显示
- 移动设备体验良好
- 触摸操作响应正确
- 布局自适应正常

### 10. 数据一致性测试

#### TC023: 前后端数据同步
**测试步骤:**
1. 在前端创建数据
2. 直接查询数据库验证
3. 在前端修改数据
4. 验证数据库更新
5. 删除数据并验证

**预期结果:**
- 创建的数据正确保存
- 修改操作正确同步
- 删除操作正确执行
- 数据库状态一致

#### TC024: 并发操作测试
**测试步骤:**
1. 多个浏览器窗口同时操作
2. 同时编辑同一视频
3. 同时删除同一数据
4. 验证数据一致性

**预期结果:**
- 并发操作不冲突
- 数据状态保持一致
- 适当的冲突处理
- 用户操作不丢失

## 📊 测试执行记录

### 测试环境信息
- 测试日期: ___________
- 测试人员: ___________
- 前端版本: ___________
- 后端版本: ___________
- 浏览器: ___________

### 测试结果统计
- 总测试用例数: 24
- 通过用例数: ___/24
- 失败用例数: ___/24
- 阻塞用例数: ___/24
- 通过率: ___%

## 🐛 缺陷报告模板

### 缺陷 ID: BUG-001
- **标题**: [简短描述]
- **严重程度**: 高/中/低
- **优先级**: P1/P2/P3
- **测试用例**: TC###
- **复现步骤**: 
  1. 步骤1
  2. 步骤2
  3. 步骤3
- **预期结果**: [描述预期行为]
- **实际结果**: [描述实际行为]
- **环境信息**: [浏览器、版本等]
- **附件**: [截图、日志等]

## ✅ 测试完成标准

1. 所有核心功能测试用例通过率 ≥ 95%
2. 无 P1 级别的阻塞性缺陷
3. P2 级别缺陷数量 ≤ 3个
4. 用户体验测试满意度 ≥ 90%
5. 性能测试指标达标
6. 数据一致性验证通过

## 📋 测试检查清单

- [ ] 测试环境准备完成
- [ ] 测试数据准备完成
- [ ] 所有测试用例执行完成
- [ ] 缺陷报告已提交
- [ ] 回归测试已完成
- [ ] 测试报告已生成
- [ ] 测试结果已评审
- [ ] 发布准备就绪

---

**注意事项:**
1. 每个测试用例都应该记录详细的执行结果
2. 发现问题时立即记录，包含详细的复现步骤
3. 关注用户体验，不仅仅是功能正确性
4. 测试过程中注意观察控制台错误和网络请求
5. 建议使用不同浏览器进行兼容性测试
