# JWT 认证集成指南

## 🎯 概述

你的 M2 前端现在已经完全集成了 JWT 认证功能，可以自动获取、管理和使用 JWT token 来调用受保护的 API。

## ✅ 已实现的功能

### 1. **自动 JWT 管理**
- ✅ 自动获取 JWT token
- ✅ 自动检测 token 过期并刷新
- ✅ 自动在所有 API 请求中添加 Authorization header
- ✅ 处理认证失败和自动重试

### 2. **开发环境支持**
- ✅ 使用 Mock 用户数据进行测试
- ✅ 支持长期有效的开发 JWT（24小时）
- ✅ 开发工具页面用于测试和调试

### 3. **生产环境就绪**
- ✅ 支持 refresh token 自动刷新
- ✅ 与主 App 的 JWT 系统兼容
- ✅ 错误处理和用户友好提示

## 🚀 使用方法

### 开发环境测试

1. **启动后端服务器**：
   ```bash
   cd apps/realmaster-video-backend
   RMBASE_FILE_CFG=config.toml ./api
   ```

2. **启动前端开发服务器**：
   ```bash
   cd apps/frontend_m2_uploader
   npm run dev
   ```

3. **访问开发工具**：
   - 打开浏览器访问 M2 前端
   - 点击 Dashboard 页面的 "🔧 Dev Tools" 按钮
   - 或直接访问 `#/dev-tools`

### 开发工具功能

#### JWT 状态检查
- 显示当前 JWT 的有效性
- 显示用户信息（ID、角色、过期时间）
- 显示 JWT 详细内容

#### JWT 管理
- **🔄 刷新 JWT**：手动刷新 token
- **🗑️ 清除 JWT**：清除所有认证信息
- **✅ 检查状态**：重新检查 JWT 状态

#### API 测试
- 测试各种受保护的 API 端点
- 实时查看请求结果
- 验证 JWT 认证是否正常工作

#### 操作日志
- 实时显示 JWT 操作日志
- 成功/失败状态提示
- 详细的错误信息

## 🔧 API 使用示例

### 在你的代码中使用

```javascript
import { 
  getVideos, 
  getCurrentUser, 
  isAuthenticated,
  refreshAuth,
  logout 
} from '../services/apiClient';

// 1. 检查认证状态
if (isAuthenticated()) {
  console.log('用户已认证');
  
  // 2. 获取当前用户信息
  const userInfo = getCurrentUser();
  console.log('用户ID:', userInfo.userId);
  console.log('用户角色:', userInfo.roles);
}

// 3. 调用 API（自动添加 JWT）
try {
  const videos = await getVideos({ page: 1, limit: 10 });
  console.log('视频列表:', videos);
} catch (error) {
  console.error('API 调用失败:', error);
}

// 4. 手动刷新 JWT
const refreshed = await refreshAuth();
if (refreshed) {
  console.log('JWT 刷新成功');
}

// 5. 登出
logout();
```

### 自动功能

所有现有的 API 调用都会自动：
- 添加 JWT token 到请求头
- 处理 token 过期和自动刷新
- 重试失败的认证请求

## 🔄 认证流程

### 开发环境流程
```
1. 页面加载 → JWT Manager 初始化
2. 检查本地存储的 JWT
3. 如果没有或过期 → 自动获取新 JWT
4. 使用 Mock 用户的 wk 调用 /auth/convertjwt
5. 获得 JWT 并存储到 localStorage
6. 所有 API 请求自动携带 JWT
```

### 生产环境流程
```
1. 主 App 传递 JWT 给 M2
2. M2 存储 JWT 到 localStorage
3. JWT Manager 自动管理 token 生命周期
4. 过期前自动使用 refresh token 刷新
5. 所有 API 请求自动携带最新 JWT
```

## 🛠️ 配置说明

### 后端配置
```toml
# config.toml
[auth]
jwtSecret = "your-jwt-secret-key"

[dbs.data]
uri = "mongodb://your-mongo-uri/realmaster_video"
```

### 前端配置
```javascript
// src/js/config.js
export const API_BASE_URL = 'http://localhost:8080';
```

## 🐛 故障排除

### 常见问题

1. **JWT 获取失败**
   - 检查后端服务器是否运行
   - 检查 Mock 用户数据是否创建成功
   - 查看开发工具的操作日志

2. **API 调用返回 401**
   - JWT 可能已过期
   - 点击"刷新 JWT"按钮
   - 检查后端 JWT 密钥配置

3. **开发工具页面无法访问**
   - 确保路由正确注册
   - 检查浏览器控制台错误
   - 尝试刷新页面

### 调试技巧

1. **查看浏览器控制台**：
   ```javascript
   // 检查 JWT 状态
   console.log('JWT:', localStorage.getItem('dev_jwt'));
   
   // 检查用户信息
   import { getCurrentUser } from './services/apiClient';
   console.log('用户信息:', getCurrentUser());
   ```

2. **使用开发工具页面**：
   - 实时查看 JWT 状态
   - 测试 API 调用
   - 查看详细日志

3. **检查网络请求**：
   - 打开浏览器开发者工具
   - 查看 Network 标签
   - 确认请求头包含 Authorization

## 🎉 总结

你的 M2 前端现在具备了完整的 JWT 认证功能：

- ✅ **开发友好**：自动 Mock 数据，长期 JWT，开发工具
- ✅ **生产就绪**：自动刷新，错误处理，与主 App 兼容
- ✅ **用户友好**：透明的认证管理，无需手动操作
- ✅ **开发者友好**：详细日志，调试工具，错误提示

现在你可以专注于业务逻辑开发，JWT 认证会在后台自动处理！🚀
