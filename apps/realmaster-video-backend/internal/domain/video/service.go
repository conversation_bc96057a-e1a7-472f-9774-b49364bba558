package video

import (
	"context"
	"errors"
	"fmt"
	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/uploader"
	"time"

	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

const unpublishedPrefix = "unpublished_"

var (
	ErrVideoAlreadyPublished     = errors.New("视频已被发布")
	ErrInvalidVideoStatus        = errors.New("视频状态不正确，无法执行操作")
	ErrVideoInProcessing         = errors.New("无法修改正在处理中的视频")
	ErrCannotDeleteProcessing    = errors.New("无法删除正在处理中的视频，请稍后再试")
	ErrInvalidVideoID            = errors.New("无效的视频ID格式")
	ErrVideoFileRequired         = errors.New("视频文件不能为空")
	ErrThumbnailRequired         = errors.New("封面图片不能为空")
	ErrInvalidRepublishOperation = errors.New("无效的操作: 'republish' 动作必须与新上传的视频文件一起使用")
)

// Service 定义了视频相关的业务逻辑接口
type Service interface {
	CreateDraft(ctx context.Context, req CreateVideoRequest) (*Video, error)
	PublishVideo(ctx context.Context, videoID string) error
	GetVideoByID(ctx context.Context, videoID string) (*VideoResponse, error)
	UpdateStats(ctx context.Context, videoID string, req UpdateStatsRequest) error
	FindVideos(ctx context.Context, filter VideoFilter) ([]VideoResponse, *common.Pagination, error)
	GetVideoStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error)
	DeleteVideo(ctx context.Context, id string) error
	UpdateVideo(ctx context.Context, req UpdateVideoRequest) (*Video, error)
	// 未来可添加的方法: UpdateDraft, ListVideos, 等.
}

type service struct {
	repo          Repository
	cfg           *config.Config
	uploadService *uploader.GoUploadService
}

// NewService 创建一个新的视频服务实例
func NewService(repo Repository, cfg *config.Config) Service {
	// 初始化goupload服务
	uploadService, err := uploader.NewGoUploadService(cfg.UserUpload.Site)
	if err != nil {
		logger.Log.Fatal("初始化上传服务失败", logger.Error(err))
	}

	return &service{
		repo:          repo,
		cfg:           cfg,
		uploadService: uploadService,
	}
}

// GetVideoByID 通过ID获取单个视频
func (s *service) GetVideoByID(ctx context.Context, videoID string) (*VideoResponse, error) {
	video, err := s.repo.FindByID(ctx, videoID)
	if err != nil {
		return nil, err
	}
	return s.toVideoResponse(video), nil
}

// UpdateStatsRequest 定义了更新视频统计数据的请求结构
// 使用指针类型以支持部分更新，客户端可以只发送需要增加的字段
type UpdateStatsRequest struct {
	Views       *int64 `json:"views"`
	Likes       *int64 `json:"likes"`
	Collections *int64 `json:"collections"`
	Completions *int64 `json:"completions"`
}

// UpdateStats 调用仓库层来原子性地更新视频统计信息
func (s *service) UpdateStats(ctx context.Context, videoID string, req UpdateStatsRequest) error {
	var views, likes, collections, completions int64

	// 如果指针非nil，则使用其值，否则默认为0
	if req.Views != nil {
		views = *req.Views
	}
	if req.Likes != nil {
		likes = *req.Likes
	}
	if req.Collections != nil {
		collections = *req.Collections
	}
	if req.Completions != nil {
		completions = *req.Completions
	}

	// 在这里可以添加业务逻辑验证，例如检查值是否为负数等
	if views < 0 || likes < 0 || collections < 0 || completions < 0 {
		return errors.New("统计增量不能为负数")
	}

	return s.repo.IncrementStats(
		ctx,
		videoID,
		views,
		likes,
		collections,
		completions,
	)
}

// CreateDraft handles the logic of creating a new video draft.
func (s *service) CreateDraft(ctx context.Context, req CreateVideoRequest) (*Video, error) {

	// 验证并转换 CategoryID 和 ClientID
	var categoryObjectID, clientObjectID primitive.ObjectID
	var err error
	if req.CategoryID != "" {
		categoryObjectID, err = primitive.ObjectIDFromHex(req.CategoryID)
		if err != nil {
			return nil, fmt.Errorf("%w: %w", common.ErrInvalidCategoryID, err)
		}
	}
	if req.ClientID != "" {
		clientObjectID, err = primitive.ObjectIDFromHex(req.ClientID)
		if err != nil {
			return nil, fmt.Errorf("%w: %w", common.ErrInvalidClientID, err)
		}
	}

	video := &Video{
		ID:          primitive.NewObjectID(),
		Title:       req.Title,       // 使用新的嵌套结构
		Description: req.Description, // 使用新的嵌套结构
		UploaderID:  req.UploaderID,
		CategoryID:  categoryObjectID,
		Tags:        req.Tags,
		PropertyIDs: req.PropertyIDs,
		ExternalURL: req.ExternalURL,
		ClientID:    clientObjectID,
		// 初始化 status, stats, timestamps 等
		// 设置goupload路径字段
		DraftVideoGouploadPath: req.DraftVideoGouploadPath,
		DraftThumbGouploadPath: req.DraftThumbGouploadPath,
		Stats: VideoStats{
			Views:          0,
			Likes:          0,
			Collections:    0,
			Completions:    0,
			CompletionRate: "0.0%",
		},
		// 移除手动时间戳，gomongo会自动添加_ts和_mt
	}

	// 根据 'publishNow' 标志决定初始状态
	if req.PublishNow {
		video.Status = StatusPending // 直接设置为 "Pending"
	} else {
		video.Status = StatusDraft // 默认为 "Draft"
	}

	if err := s.repo.Create(ctx, video); err != nil {
		return nil, err
	}

	return video, nil
}

// PublishVideo 将视频状态更新为 "Pending" 以便 worker 处理
func (s *service) PublishVideo(ctx context.Context, videoID string) error {
	video, err := s.repo.FindByID(ctx, videoID)
	if err != nil {
		return err
	}

	// 1. 检查视频是否可以被发布
	// 只有草稿或处理失败的视频才能被重新发布
	if video.Status != StatusDraft && video.Status != StatusProcessingFailed {
		return fmt.Errorf("%w: 当前状态为 '%s'", ErrInvalidVideoStatus, video.Status)
	}

	// 2. 更新状态为"待处理"
	// Worker 将会轮询这个状态的视频
	video.Status = StatusPending
	// 移除手动时间戳设置，gomongo会自动更新_mt
	if err := s.repo.Update(ctx, video); err != nil {
		return fmt.Errorf("更新视频状态为待处理失败: %w", err)
	}

	return nil
}

// FindVideos retrieves a paginated list of videos based on filters.
func (s *service) FindVideos(ctx context.Context, filter VideoFilter) ([]VideoResponse, *common.Pagination, error) {
	videos, total, err := s.repo.Find(ctx, filter)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to find videos: %w", err)
	}

	// Convert DB models to API response DTOs
	videoResponses := make([]VideoResponse, len(videos))
	for i := range videos {
		videoResponses[i] = *s.toVideoResponse(&videos[i])
	}

	pagination := common.CalculatePagination(total, filter.Page, filter.Limit)

	return videoResponses, pagination, nil
}

// GetVideoStats retrieves aggregated video stats based on the filter.
func (s *service) GetVideoStats(ctx context.Context, filter VideoFilter) (*VideoStatsSummary, error) {
	return s.repo.GetAggregatedStats(ctx, filter)
}

// deleteAssociatedFiles 使用goupload删除视频相关的所有文件
func (s *service) deleteAssociatedFiles(video *Video) error {
	// 使用service中的uploadService

	var errorMessages []string

	// 1. 删除草稿文件
	if video.DraftVideoGouploadPath != "" {
		if err := s.uploadService.DeleteVideoDraft(context.Background(), video.DraftVideoGouploadPath); err != nil {
			logger.Log.Error("删除草稿视频文件失败", logger.String("path", video.DraftVideoGouploadPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("删除草稿视频失败: %s", video.DraftVideoGouploadPath))
		} else {
			logger.Log.Info("草稿视频文件删除成功", logger.String("path", video.DraftVideoGouploadPath))
		}
	}

	if video.DraftThumbGouploadPath != "" {
		if err := s.uploadService.DeleteThumbnailDraft(context.Background(), video.DraftThumbGouploadPath); err != nil {
			logger.Log.Error("删除草稿缩略图文件失败", logger.String("path", video.DraftThumbGouploadPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("删除草稿缩略图失败: %s", video.DraftThumbGouploadPath))
		} else {
			logger.Log.Info("草稿缩略图文件删除成功", logger.String("path", video.DraftThumbGouploadPath))
		}
	}

	// 2. 删除最终视频目录
	if video.FinalVideoGouploadPath != "" {
		if err := s.uploadService.DeleteVideoFinal(context.Background(), video.FinalVideoGouploadPath); err != nil {
			logger.Log.Error("删除最终视频目录失败", logger.String("path", video.FinalVideoGouploadPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("删除最终视频目录失败: %s", video.FinalVideoGouploadPath))
		} else {
			logger.Log.Info("最终视频目录删除成功", logger.String("path", video.FinalVideoGouploadPath))
		}
	}

	if video.FinalThumbGouploadPath != "" {
		if err := s.uploadService.DeleteThumbnailFinal(context.Background(), video.FinalThumbGouploadPath); err != nil {
			logger.Log.Error("删除最终缩略图文件失败", logger.String("path", video.FinalThumbGouploadPath), logger.Error(err))
			errorMessages = append(errorMessages, fmt.Sprintf("删除最终缩略图失败: %s", video.FinalThumbGouploadPath))
		} else {
			logger.Log.Info("最终缩略图文件删除成功", logger.String("path", video.FinalThumbGouploadPath))
		}
	}

	if len(errorMessages) > 0 {
		return fmt.Errorf("删除文件时遇到错误: %s", strings.Join(errorMessages, "; "))
	}

	return nil
}

// DeleteVideo handles the logic for deleting a video and its associated files.
// The operation is atomic: it first deletes files, and only upon success, deletes the database record.
func (s *service) DeleteVideo(ctx context.Context, id string) error {
	// 1. Get video info from DB to know which files to delete.
	video, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			// If the DB record doesn't exist, the desired state is already achieved.
			// This is not an error in the context of a delete operation.
			return nil
		}
		return err // Handle other potential database errors.
	}

	// 2. Perform business logic checks. For example, prevent deletion of a video that is actively being processed.
	if video.Status == StatusProcessing {
		return ErrCannotDeleteProcessing
	}

	// 3. Delete all associated files from the filesystem FIRST.
	// This is the most critical step. If this fails, we abort and leave the DB record intact for a retry.
	if err := s.deleteAssociatedFiles(video); err != nil {
		return fmt.Errorf("failed to delete associated files, database record is preserved for retry: %w", err)
	}

	// 4. Only after all files are successfully deleted, delete the database record.
	if err := s.repo.DeleteByID(ctx, video.ID); err != nil {
		// This is a critical failure state where files are gone but the DB record remains.
		// Log as error but don't crash the application
		logger.Log.Error("CRITICAL: Files deleted but failed to delete DB record - manual intervention required",
			logger.String("videoID", id),
			logger.String("status", "files_deleted_db_failed"),
			logger.Error(err),
		)
		// TODO: 考虑实现补偿机制，如将记录标记为"待清理"状态
		return fmt.Errorf("files were deleted, but failed to delete the database record. Manual intervention required for video ID '%s': %w", id, err)
	}

	return nil
}

// toVideoResponse converts a Video DB model to a VideoResponse API DTO.
// This is where we populate the virtual preview URL fields.
func (s *service) toVideoResponse(video *Video) *VideoResponse {
	resp := &VideoResponse{
		ID:              video.ID,
		Title:           video.Title,
		Description:     video.Description,
		Status:          video.Status,
		Duration:        video.Duration,
		UploaderID:      video.UploaderID,
		CategoryID:      video.CategoryID,
		Tags:            video.Tags,
		PropertyIDs:     video.PropertyIDs,
		ExternalURL:     video.ExternalURL,
		ClientID:        video.ClientID,
		Stats:           video.Stats,
		ProcessingError: video.ProcessingError,
		// 移除手动时间戳字段，如需要可从gomongo的_ts和_mt获取
		// CreatedAt:       video.CreatedAt,
		// UpdatedAt:       video.UpdatedAt,
		PublishedAt: video.PublishedAt,
	}

	// 基于goupload路径和视频状态动态生成预览URL
	switch video.Status {
	case StatusDraft, StatusPending, StatusProcessing, StatusProcessingFailed:
		// 草稿/待处理/处理中/处理失败状态：使用草稿goupload路径
		if video.DraftVideoGouploadPath != "" {
			resp.PreviewVideoUrl = s.buildGouploadURL("video_draft", video.DraftVideoGouploadPath, false)
		}
		if video.DraftThumbGouploadPath != "" {
			resp.PreviewThumbUrl = s.buildGouploadURL("thumbnail_draft", video.DraftThumbGouploadPath, false)
		}

	case StatusPublished:
		// 已发布状态：使用最终goupload路径
		if video.FinalVideoGouploadPath != "" {
			resp.PreviewVideoUrl = s.buildGouploadURL("video_final", video.FinalVideoGouploadPath, false)
		}
		if video.FinalThumbGouploadPath != "" {
			resp.PreviewThumbUrl = s.buildGouploadURL("thumbnail_final", video.FinalThumbGouploadPath, false)
		}
	case StatusUnpublished:
		// 下架状态：使用相同的entryName但添加unpublished前缀到目录名
		if video.FinalVideoGouploadPath != "" {
			resp.PreviewVideoUrl = s.buildGouploadURL("video_final", video.FinalVideoGouploadPath, true)
		}
		if video.FinalThumbGouploadPath != "" {
			resp.PreviewThumbUrl = s.buildGouploadURL("thumbnail_final", video.FinalThumbGouploadPath, true)
		}
	}
	return resp
}

// UpdateVideo handles the complex logic of updating a video using goupload.
func (s *service) UpdateVideo(ctx context.Context, req UpdateVideoRequest) (*Video, error) {
	// 1. Get current video state
	video, err := s.repo.FindByID(ctx, req.ID.Hex())
	if err != nil {
		return nil, err
	}

	// 2. Check for permissions based on state
	switch video.Status {
	case StatusPending, StatusProcessing:
		return nil, ErrVideoInProcessing
	}

	updateData := bson.M{}
	var newStatus string

	// 3. Process file updates (if a new video file is provided via form or chunked upload path)
	isNewVideoProvided := req.DraftVideoFile != nil || req.DraftVideoGouploadPath != ""
	if isNewVideoProvided {
		// --- Step 1: Determine new status ---
		if req.Action == "publish" || req.Action == "republish" {
			newStatus = StatusPending
		} else { // "save_as_draft"
			newStatus = StatusDraft
		}
		updateData["st"] = newStatus

		// --- Step 2: Delete old goupload files if they exist ---
		if video.DraftVideoGouploadPath != "" {
			if err := s.uploadService.DeleteVideoDraft(ctx, video.DraftVideoGouploadPath); err != nil {
				logger.Log.Error("删除旧草稿视频文件失败", logger.Error(err))
				// 继续执行，不阻断流程
			}
		}

		// 删除旧最终视频文件（如果存在）
		if video.FinalVideoGouploadPath != "" {
			if err := s.uploadService.DeleteVideoFinal(ctx, video.FinalVideoGouploadPath); err != nil {
				logger.Log.Error("删除旧最终视频目录失败", logger.String("path", video.FinalVideoGouploadPath), logger.Error(err))
				// 继续执行，不阻断流程
			} else {
				logger.Log.Info("旧最终视频目录删除成功", logger.String("path", video.FinalVideoGouploadPath))
			}
		}

		// 注意：最终缩略图的删除将在缩略图处理逻辑中进行，确保正确的顺序

		// --- Step 3: Handle new video file ---
		var newVideoPath string
		if req.DraftVideoFile != nil {
			// Upload new video file from multipart form
			videoFile, err := req.DraftVideoFile.Open()
			if err != nil {
				return nil, fmt.Errorf("打开视频文件失败: %w", err)
			}
			defer videoFile.Close()

			uploadResult, err := s.uploadService.UploadVideoDraft(ctx, video.UploaderID, videoFile, req.DraftVideoFile.Filename, req.DraftVideoFile.Size)
			if err != nil {
				return nil, fmt.Errorf("上传新视频文件失败: %w", err)
			}
			newVideoPath = uploadResult.Path
		} else {
			// Use path from chunked upload
			newVideoPath = req.DraftVideoGouploadPath
		}
		updateData["draftVideoGouploadPath"] = newVideoPath

		// --- Step 4: Clear final video fields (重新上传视频，清空最终文件) ---
		updateData["finalVideoGouploadPath"] = ""
		updateData["finalThumbGouploadPath"] = "" // 也清空最终封面
		updateData["procErr"] = ""
		updateData["drt"] = 0
		updateData["manifestsBaseName"] = ""

		// --- Step 5: Handle thumbnail ---
		isNewThumbProvided := req.DraftThumbnailFile != nil || req.DraftThumbGouploadPath != ""

		if isNewThumbProvided {
			// 有新缩略图：删除旧草稿缩略图和旧最终缩略图，上传新缩略图
			if video.DraftThumbGouploadPath != "" {
				if err := s.uploadService.DeleteThumbnailDraft(ctx, video.DraftThumbGouploadPath); err != nil {
					logger.Log.Error("删除旧草稿缩略图失败", logger.Error(err))
				}
			}

			// 删除旧最终缩略图文件（如果存在）
			if video.FinalThumbGouploadPath != "" {
				if err := s.uploadService.DeleteThumbnailFinal(ctx, video.FinalThumbGouploadPath); err != nil {
					logger.Log.Error("删除旧最终缩略图文件失败", logger.Error(err))
					// 继续执行，不阻断流程
				}
			}

			var newThumbPath string
			if req.DraftThumbnailFile != nil {
				// Upload new thumbnail from multipart form
				thumbFile, err := req.DraftThumbnailFile.Open()
				if err != nil {
					return nil, fmt.Errorf("打开缩略图文件失败: %w", err)
				}
				defer thumbFile.Close()

				thumbResult, err := s.uploadService.UploadThumbnailDraft(ctx, video.UploaderID, thumbFile, req.DraftThumbnailFile.Filename, req.DraftThumbnailFile.Size)
				if err != nil {
					return nil, fmt.Errorf("上传新缩略图失败: %w", err)
				}
				newThumbPath = thumbResult.Path
			} else {
				// Use path from chunked upload
				newThumbPath = req.DraftThumbGouploadPath
			}
			updateData["draftThumbGouploadPath"] = newThumbPath
		} else {
			// 没有新缩略图：根据视频状态决定处理方式
			originalStatus := video.Status
			if originalStatus == StatusDraft {
				// Draft状态：保持原有草稿缩略图不变
				// 不需要做任何操作
			} else if originalStatus == StatusPublished || originalStatus == StatusUnpublished {
				// Published/Unpublished状态：将旧最终缩略图重新上传到草稿位置，然后删除旧最终缩略图
				if video.FinalThumbGouploadPath != "" {
					// 删除旧草稿缩略图（如果存在）
					if video.DraftThumbGouploadPath != "" {
						if err := s.uploadService.DeleteThumbnailDraft(ctx, video.DraftThumbGouploadPath); err != nil {
							logger.Log.Error("删除旧草稿缩略图失败", logger.Error(err))
						}
					}

					// 先将最终缩略图重新上传到草稿位置
					thumbResult, err := s.uploadService.ReuploadThumbnailFinalToDraft(ctx, video.UploaderID, video.FinalThumbGouploadPath)
					if err != nil {
						return nil, fmt.Errorf("重新上传缩略图到草稿位置失败: %w", err)
					}
					updateData["draftThumbGouploadPath"] = thumbResult.Path

					// 重新上传成功后，删除旧最终缩略图文件
					if err := s.uploadService.DeleteThumbnailFinal(ctx, video.FinalThumbGouploadPath); err != nil {
						logger.Log.Error("删除旧最终缩略图文件失败", logger.Error(err))
						// 继续执行，不阻断流程
					}
				}
			} else if originalStatus == StatusProcessingFailed {
				// ProcessingFailed状态：保持原有草稿缩略图不变
				// 不需要做任何操作
			}
		}

	} else {
		// Logic for when NO new video file is uploaded
		originalStatus := video.Status
		newStatus = originalStatus

		switch req.Action {
		case "publish":
			switch originalStatus {
			case StatusUnpublished:
				// 从下架状态重新发布：直接变为已发布，更新URL去掉unpublished前缀，并重命名实际文件
				newStatus = StatusPublished

				if video.FinalVideoGouploadPath != "" {
					newVideoPath := s.removeUnpublishedPrefix(video.FinalVideoGouploadPath)
					// 重命名整个视频目录（去掉unpublished前缀）
					if err := s.uploadService.RenameVideoDirectory(ctx, video.FinalVideoGouploadPath, newVideoPath); err != nil {
						logger.Log.Error("重命名视频目录失败",
							logger.String("oldPath", video.FinalVideoGouploadPath),
							logger.String("newPath", newVideoPath),
							logger.Error(err))
						return nil, fmt.Errorf("重命名视频目录失败: %w", err)
					}
					updateData["finalVideoGouploadPath"] = newVideoPath
				}

				if video.FinalThumbGouploadPath != "" {
					newThumbPath := s.removeUnpublishedPrefix(video.FinalThumbGouploadPath)
					// 重命名实际的缩略图文件（去掉unpublished前缀）
					if err := s.uploadService.RenameThumbnailFinal(ctx, video.FinalThumbGouploadPath, newThumbPath); err != nil {
						logger.Log.Error("重命名缩略图文件失败",
							logger.String("oldPath", video.FinalThumbGouploadPath),
							logger.String("newPath", newThumbPath),
							logger.Error(err))
						return nil, fmt.Errorf("重命名缩略图文件失败: %w", err)
					}
					updateData["finalThumbGouploadPath"] = newThumbPath
				}
			case StatusDraft, StatusProcessingFailed:
				newStatus = StatusPending
			}
		case "unpublish":
			if originalStatus == StatusPublished {
				newStatus = StatusUnpublished

				// 下架：在URL中添加unpublished前缀，并重命名实际目录
				if video.FinalVideoGouploadPath != "" {
					newVideoPath := s.addUnpublishedPrefix(video.FinalVideoGouploadPath)
					// 重命名整个视频目录（包含所有m3u8文件和视频片段）
					if err := s.uploadService.RenameVideoDirectory(ctx, video.FinalVideoGouploadPath, newVideoPath); err != nil {
						logger.Log.Error("重命名视频目录失败",
							logger.String("oldPath", video.FinalVideoGouploadPath),
							logger.String("newPath", newVideoPath),
							logger.Error(err))
						return nil, fmt.Errorf("重命名视频目录失败: %w", err)
					}
					updateData["finalVideoGouploadPath"] = newVideoPath
				}

				if video.FinalThumbGouploadPath != "" {
					newThumbPath := s.addUnpublishedPrefix(video.FinalThumbGouploadPath)
					// 重命名缩略图文件
					if err := s.uploadService.RenameThumbnailFinal(ctx, video.FinalThumbGouploadPath, newThumbPath); err != nil {
						logger.Log.Error("重命名缩略图文件失败",
							logger.String("oldPath", video.FinalThumbGouploadPath),
							logger.String("newPath", newThumbPath),
							logger.Error(err))
						return nil, fmt.Errorf("重命名缩略图文件失败: %w", err)
					}
					updateData["finalThumbGouploadPath"] = newThumbPath
				}
			}
		case "republish":
			return nil, ErrInvalidRepublishOperation
		}

		// Update status if changed
		if newStatus != originalStatus {
			updateData["st"] = newStatus
			if newStatus == StatusPublished {
				updateData["pts"] = time.Now()
			}
		}

		// Handle standalone thumbnail update for published/unpublished videos
		if req.DraftThumbnailFile != nil {
			if newStatus == StatusPublished || newStatus == StatusUnpublished {
				// Delete old final thumbnail
				if video.FinalThumbGouploadPath != "" {
					if err := s.uploadService.DeleteThumbnailFinal(ctx, video.FinalThumbGouploadPath); err != nil {
						logger.Log.Error("删除旧最终缩略图失败", logger.Error(err))
					}
				}
				// Upload new thumbnail directly to final location
				thumbFile, err := req.DraftThumbnailFile.Open()
				if err != nil {
					return nil, fmt.Errorf("打开缩略图文件失败: %w", err)
				}
				defer thumbFile.Close()

				thumbResult, err := s.uploadService.UploadThumbnailFinal(ctx, video.UploaderID, thumbFile, req.DraftThumbnailFile.Filename, req.DraftThumbnailFile.Size)
				if err != nil {
					return nil, fmt.Errorf("上传新最终缩略图失败: %w", err)
				}

				finalThumbPath := thumbResult.Path
				// Apply unpublished prefix if needed
				if newStatus == StatusUnpublished {
					// 为unpublished状态添加前缀并重命名文件
					newThumbPath := s.addUnpublishedPrefix(thumbResult.Path)
					if err := s.uploadService.RenameThumbnailFinal(ctx, thumbResult.Path, newThumbPath); err != nil {
						logger.Log.Error("重命名缩略图文件失败",
							logger.String("oldPath", thumbResult.Path),
							logger.String("newPath", newThumbPath),
							logger.Error(err))
						return nil, fmt.Errorf("重命名缩略图文件失败: %w", err)
					}
					finalThumbPath = newThumbPath
				}
				updateData["finalThumbGouploadPath"] = finalThumbPath
			} else {
				// For draft/failed videos, update draft thumbnail
				if video.DraftThumbGouploadPath != "" {
					if err := s.uploadService.DeleteThumbnailDraft(ctx, video.DraftThumbGouploadPath); err != nil {
						logger.Log.Error("删除旧草稿缩略图失败", logger.Error(err))
					}
				}
				thumbFile, err := req.DraftThumbnailFile.Open()
				if err != nil {
					return nil, fmt.Errorf("打开缩略图文件失败: %w", err)
				}
				defer thumbFile.Close()

				thumbResult, err := s.uploadService.UploadThumbnailDraft(ctx, video.UploaderID, thumbFile, req.DraftThumbnailFile.Filename, req.DraftThumbnailFile.Size)
				if err != nil {
					return nil, fmt.Errorf("上传新草稿缩略图失败: %w", err)
				}
				updateData["draftThumbGouploadPath"] = thumbResult.Path
			}
		}
	}

	// 4. Process metadata updates
	for key, value := range req.Metadata {
		switch key {
		case "title":
			updateData["tl"] = value
		case "description":
			updateData["dsc"] = value
		case "tags":
			updateData["tags"] = value
		case "propertyIds":
			updateData["prop"] = value
		case "externalUrl":
			updateData["ExtUrl"] = value
		case "categoryId":
			if catIDStr, ok := value.(string); ok && catIDStr != "" {
				catID, err := primitive.ObjectIDFromHex(catIDStr)
				if err != nil {
					return nil, fmt.Errorf("%w: %w", common.ErrInvalidCategoryID, err)
				}
				updateData["catId"] = catID
			} else {
				updateData["catId"] = nil
			}
		case "clientId":
			if clientIDStr, ok := value.(string); ok && clientIDStr != "" {
				clientID, err := primitive.ObjectIDFromHex(clientIDStr)
				if err != nil {
					return nil, fmt.Errorf("%w: %w", common.ErrInvalidClientID, err)
				}
				updateData["ClntId"] = clientID
			} else {
				updateData["ClntId"] = nil
			}
		}
	}

	// 5. Apply updates to database
	if len(updateData) > 0 {
		if err := s.repo.UpdateSelective(ctx, req.ID, updateData); err != nil {
			return nil, err
		}
	}

	// 6. Return updated video
	return s.repo.FindByID(ctx, req.ID.Hex())
}

// buildGouploadURL 基于goupload路径和entryName构建完整的访问URL
func (s *service) buildGouploadURL(entryName, gouploadPath string, isUnpublished bool) string {
	return common.BuildGouploadURL(s.cfg.UserUpload.Site, entryName, gouploadPath, s.cfg.Media.ServerURL, isUnpublished)
}

// addUnpublishedPrefix 在goupload路径中添加unpublished前缀
func (s *service) addUnpublishedPrefix(gouploadPath string) string {
	if gouploadPath == "" {
		return ""
	}

	// goupload路径格式: USER/2025-28/abc123/filename.ext
	// 需要在目录名前添加unpublished_前缀: USER/2025-28/unpublished_abc123/filename.ext
	pathParts := strings.Split(gouploadPath, "/")
	if len(pathParts) >= 3 {
		// 在目录名前添加unpublished_前缀
		pathParts[2] = unpublishedPrefix + pathParts[2]
		return strings.Join(pathParts, "/")
	}

	return gouploadPath
}

// removeUnpublishedPrefix 从goupload路径中移除unpublished前缀
func (s *service) removeUnpublishedPrefix(gouploadPath string) string {
	if gouploadPath == "" {
		return ""
	}

	// goupload路径格式: USER/2025-28/unpublished_abc123/filename.ext
	// 需要移除目录名前的unpublished_前缀: USER/2025-28/abc123/filename.ext
	pathParts := strings.Split(gouploadPath, "/")
	if len(pathParts) >= 3 && strings.HasPrefix(pathParts[2], unpublishedPrefix) {
		// 移除目录名前的unpublished_前缀
		pathParts[2] = strings.TrimPrefix(pathParts[2], unpublishedPrefix)
		return strings.Join(pathParts, "/")
	}

	return gouploadPath
}
