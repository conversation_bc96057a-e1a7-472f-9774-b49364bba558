package common

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"realmaster-video-backend/internal/platform/logger"

	levelstore "github.com/real-rm/golevelstore"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// IsValidObjectID 检查字符串是否为有效的MongoDB ObjectID
func IsValidObjectID(id string) bool {
	_, err := primitive.ObjectIDFromHex(id)
	return err == nil
}

// CleanupEmptyDirectories 递归清理空目录
// 从给定路径开始向上清理，直到遇到非空目录或到达根目录
func CleanupEmptyDirectories(dirPath string, rootDir string) {
	// 确保不会删除根目录本身
	if dirPath == rootDir || !strings.HasPrefix(dirPath, rootDir) {
		return
	}

	// 检查目录是否为空
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		logger.Log.Debug("无法读取目录进行清理", logger.String("path", dirPath), logger.Error(err))
		return
	}

	// 如果目录为空，删除它并继续向上清理
	if len(entries) == 0 {
		if err := os.Remove(dirPath); err != nil {
			logger.Log.Debug("无法删除空目录", logger.String("path", dirPath), logger.Error(err))
			return
		}
		logger.Log.Info("已清理空目录", logger.String("path", dirPath))

		// 递归清理父目录
		parentDir := filepath.Dir(dirPath)
		CleanupEmptyDirectories(parentDir, rootDir)
	}
}

// BuildGouploadURL 基于goupload路径和entryName构建完整的访问URL
// 这是一个通用函数，用于替代video和public service中的重复代码
func BuildGouploadURL(siteName, entryName, gouploadPath, baseURL string, isUnpublished bool) string {
	if gouploadPath == "" {
		return ""
	}

	// 确保基础URL包含协议
	if !strings.HasPrefix(baseURL, "http://") && !strings.HasPrefix(baseURL, "https://") {
		baseURL = "http://" + baseURL
	}

	// 从配置中获取prefix
	prefix, err := GetPrefixFromConfig(siteName, entryName)
	if err != nil {
		logger.Log.Error("获取URL前缀失败",
			logger.String("siteName", siteName),
			logger.String("entryName", entryName),
			logger.Error(err))
		return ""
	}

	// 对于下架视频，在goupload路径中添加unpublished_前缀
	finalGouploadPath := gouploadPath
	if isUnpublished {
		// 在路径的目录部分添加unpublished_前缀
		// 例如: USER/2025-28/abc123/file.ext -> USER/2025-28/unpublished_abc123/file.ext
		pathParts := strings.Split(gouploadPath, "/")
		if len(pathParts) >= 3 {
			// 在目录名前添加unpublished_前缀
			pathParts[2] = "unpublished_" + pathParts[2]
			finalGouploadPath = strings.Join(pathParts, "/")
		}
	}

	// gouploadPath 格式: USER/2025-28/abc123/filename.ext
	return fmt.Sprintf("%s%s/%s", baseURL, prefix, finalGouploadPath)
}

// GetPrefixFromConfig 从goupload配置中动态获取entryName对应的prefix
// 这是一个通用函数，用于替代video和public service中的重复代码
func GetPrefixFromConfig(siteName, entryName string) (string, error) {
	config, err := levelstore.GetUserUploadConfig(siteName, entryName, nil)
	if err != nil {
		logger.Log.Error("获取goupload配置失败",
			logger.String("siteName", siteName),
			logger.String("entryName", entryName),
			logger.Error(err))
		return "", fmt.Errorf("获取goupload配置失败: %w", err)
	}

	return config.Prefix, nil
}

// GetLocalStoragePathFromConfig 从goupload配置中动态获取entryName对应的本地存储路径
// 用于替代硬编码的存储路径
func GetLocalStoragePathFromConfig(siteName, entryName string) (string, error) {
	config, err := levelstore.GetUserUploadConfig(siteName, entryName, nil)
	if err != nil {
		logger.Log.Error("获取goupload配置失败",
			logger.String("siteName", siteName),
			logger.String("entryName", entryName),
			logger.Error(err))
		return "", fmt.Errorf("获取goupload配置失败: %w", err)
	}

	// 查找第一个本地存储配置
	for _, storage := range config.Storage {
		if storage.Type == "local" {
			return storage.Path, nil
		}
	}

	return "", fmt.Errorf("未找到entryName '%s' 的本地存储配置", entryName)
}
