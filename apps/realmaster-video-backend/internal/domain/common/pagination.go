package common

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaginationConfig 分页配置常量
const (
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPageSize     = 1
	DefaultPage     = 1
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page  int `form:"page" json:"page"`
	Limit int `form:"limit" json:"limit"`
}

// Validate 验证分页参数
func (p *PaginationParams) Validate() {
	if p.Page <= 0 {
		p.Page = DefaultPage
	}
	if p.Limit <= 0 {
		p.Limit = DefaultPageSize
	}
	if p.Limit > MaxPageSize {
		p.Limit = MaxPageSize
	}
}

// ParsePaginationFromQuery 从查询参数解析分页信息
func ParsePaginationFromQuery(c *gin.Context) PaginationParams {
	var params PaginationParams
	
	// 解析页码
	if pageStr := c.<PERSON>ult<PERSON>("page", "1"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			params.Page = page
		}
	}
	
	// 解析每页数量
	if limitStr := c.DefaultQuery("limit", strconv.Itoa(DefaultPageSize)); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil {
			params.Limit = limit
		}
	}
	
	params.Validate()
	return params
}

// CalculatePagination 计算分页信息
func CalculatePagination(total int64, page, limit int) *Pagination {
	var totalPages int64
	if limit > 0 && total > 0 {
		totalPages = (total + int64(limit) - 1) / int64(limit)
	} else if total > 0 {
		totalPages = 1
	}
	
	return &Pagination{
		TotalItems:  total,
		TotalPages:  totalPages,
		CurrentPage: int64(page),
		Limit:       int64(limit),
	}
}

// PaginatedResponse 通用分页响应结构
type PaginatedResponse struct {
	Items      interface{} `json:"items"`
	Pagination *Pagination `json:"pgn"`
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(items interface{}, total int64, page, limit int) *PaginatedResponse {
	return &PaginatedResponse{
		Items:      items,
		Pagination: CalculatePagination(total, page, limit),
	}
}
