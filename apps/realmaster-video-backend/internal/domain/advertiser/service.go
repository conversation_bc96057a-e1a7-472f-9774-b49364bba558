package advertiser

import (
	"context"
	"errors"
	"fmt"
	"mime/multipart"

	"realmaster-video-backend/internal/config"
	"realmaster-video-backend/internal/domain/common"
	"realmaster-video-backend/internal/domain/video"
	"realmaster-video-backend/internal/platform/logger"
	"realmaster-video-backend/internal/uploader"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var (
	ErrAdvertiserNotFound       = errors.New("广告主不存在")
	ErrInvalidAdvertiserID      = errors.New("无效的广告主ID")
	ErrEmailExists              = errors.New("电子邮箱已被使用")
	ErrPhoneExists              = errors.New("电话号码已被使用")
	ErrNameExists               = errors.New("广告主名称已被使用")
	ErrInvalidEmailFormat       = errors.New("无效的电子邮箱格式")
	ErrInvalidPhoneFormat       = errors.New("无效的电话号码格式")
	ErrCannotDeleteLast         = errors.New("无法删除唯一的广告主")
	ErrMergeToSelf              = errors.New("不能将广告主合并到其自身")
	ErrTargetAdvertiserNotFound = errors.New("目标广告主不存在")
)

// Service 定义了广告主相关的业务逻辑接口
type Service interface {
	List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error)
	GetByID(ctx context.Context, id string) (*Advertiser, error)
	Create(ctx context.Context, userID string, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error)
	Update(ctx context.Context, userID string, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error
	Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error
}

type service struct {
	repo          Repository
	videoRepo     video.Repository
	cfg           *config.Config
	uploadService *uploader.GoUploadService
}

// NewService 创建一个新的广告主服务实例
func NewService(repo Repository, videoRepo video.Repository, cfg *config.Config) Service {
	// 初始化 goupload 服务
	uploadService, err := uploader.NewGoUploadService(cfg.UserUpload.Site)
	if err != nil {
		logger.Log.Fatal("初始化上传服务失败", logger.Error(err))
	}

	return &service{
		repo:          repo,
		videoRepo:     videoRepo,
		cfg:           cfg,
		uploadService: uploadService,
	}
}

// List 获取广告主列表（带分页和筛选）
func (s *service) List(ctx context.Context, req ListAdvertisersRequest) (*ListAdvertisersResponseData, error) {
	// 1. 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10 // 默认每页10条
	}

	// 2. 构建查询 filter
	filter := bson.M{}
	if req.Name != "" {
		// 使用 primitive.Regex 实现模糊查询
		filter["nm"] = primitive.Regex{Pattern: req.Name, Options: "i"} // i 表示不区分大小写
	}
	if req.Phone != "" {
		filter["ph"] = req.Phone
	}
	if req.Email != "" {
		filter["em"] = req.Email
	}

	// 3. 并行执行查询和计数
	var advertisers []Advertiser
	var total int64
	var findErr, countErr error

	errChan := make(chan error, 2)
	go func() {
		advertisers, findErr = s.repo.Find(ctx, filter, req.Page, req.Limit)
		errChan <- findErr
	}()
	go func() {
		total, countErr = s.repo.Count(ctx, filter)
		errChan <- countErr
	}()

	for i := 0; i < 2; i++ {
		if err := <-errChan; err != nil {
			return nil, err
		}
	}

	// 4. 计算总页数
	var totalPages int64
	if req.Limit > 0 {
		totalPages = (total + req.Limit - 1) / req.Limit
	} else if total > 0 {
		// limit=0 或未提供时，表示获取所有，即只有一页
		totalPages = 1
	}

	// 5. 组装并返回响应数据
	return &ListAdvertisersResponseData{
		Items: advertisers,
		Pagination: &common.Pagination{
			TotalItems:  total,
			TotalPages:  totalPages,
			CurrentPage: req.Page,
			Limit:       req.Limit,
		},
	}, nil
}

// GetByID 获取单个广告主详情
func (s *service) GetByID(ctx context.Context, id string) (*Advertiser, error) {
	if _, err := primitive.ObjectIDFromHex(id); err != nil {
		return nil, ErrInvalidAdvertiserID
	}

	advertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err // 直接传递仓库层的错误
	}
	if advertiser == nil {
		return nil, ErrAdvertiserNotFound
	}
	return advertiser, nil
}

// saveAvatarWithGoupload 使用goupload保存头像文件并返回其可访问的 URL
func (s *service) saveAvatarWithGoupload(ctx context.Context, userID string, fileHeader *multipart.FileHeader) (string, error) {
	// 打开上传的文件
	file, err := fileHeader.Open()
	if err != nil {
		return "", fmt.Errorf("无法打开上传的文件: %w", err)
	}
	defer file.Close()

	// 使用goupload上传头像
	result, err := s.uploadService.UploadClientAvatar(
		ctx,
		userID,
		file,
		fileHeader.Filename,
		fileHeader.Size,
	)
	if err != nil {
		return "", fmt.Errorf("上传头像失败: %w", err)
	}

	// 构建包含前缀的完整URL路径
	// 使用common.BuildGouploadURL来构建完整的URL路径（不包含baseURL，只包含路径部分）
	prefix, err := common.GetPrefixFromConfig(s.cfg.UserUpload.Site, "client_avatar")
	if err != nil {
		return "", fmt.Errorf("获取头像URL前缀失败: %w", err)
	}

	// 返回包含前缀的完整路径，格式: /media/avatars/user/2025-28/abc123/avatar.jpg
	return fmt.Sprintf("%s/%s", prefix, result.Path), nil
}

// Create 创建一个新的广告主
func (s *service) Create(ctx context.Context, userID string, req CreateAdvertiserRequest, avatarFile *multipart.FileHeader) (*Advertiser, error) {
	// 1. 检查唯一性和格式
	if req.Email != "" {
		if !common.IsValidEmail(req.Email) {
			return nil, ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmail(ctx, req.Email)
		if err != nil {
			return nil, err
		}
		if emailExists {
			return nil, ErrEmailExists
		}
	}

	if req.Phone != "" {
		if !common.IsValidPhone(req.Phone) {
			return nil, ErrInvalidPhoneFormat
		}
		phoneExists, err := s.repo.ExistsByPhone(ctx, req.Phone)
		if err != nil {
			return nil, err
		}
		if phoneExists {
			return nil, ErrPhoneExists
		}
	}

	// 处理头像上传
	var avatarURL string
	if avatarFile != nil {
		var err error
		avatarURL, err = s.saveAvatarWithGoupload(ctx, userID, avatarFile)
		if err != nil {
			return nil, fmt.Errorf("保存头像失败: %w", err)
		}
	}

	// 2. 创建实体
	advertiser := &Advertiser{
		Name:      req.Name,
		AvatarURL: avatarURL,
		Phone:     req.Phone,
		Email:     req.Email,
		MAppUID:   req.MAppUID,
		Remark:    req.Remark,
		// gomongo会自动添加_ts和_mt字段
	}

	// 3. 保存到数据库
	if err := s.repo.Create(ctx, advertiser); err != nil {
		// 注意：goupload已经处理了文件管理，这里不需要手动清理文件
		return nil, fmt.Errorf("service: 创建广告主失败: %w", err)
	}

	return advertiser, nil
}

// Update 更新一个广告主
func (s *service) Update(ctx context.Context, userID string, id string, req UpdateAdvertiserRequest, avatarFile *multipart.FileHeader) error {
	// 0. 验证ID格式
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return ErrInvalidAdvertiserID
	}

	// 1. 获取当前广告主数据
	existingAdvertiser, err := s.repo.FindByID(ctx, id)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return err
	}

	// 2. 处理头像更新
	var newAvatarURL string

	newAvatarURL = existingAdvertiser.AvatarURL
	if avatarFile != nil {
		// 使用goupload保存新头像
		savedURL, err := s.saveAvatarWithGoupload(ctx, userID, avatarFile)
		if err != nil {
			return fmt.Errorf("保存新头像失败: %w", err)
		}
		newAvatarURL = savedURL
	}

	// 3. 准备更新的数据
	updateData := bson.M{}

	// Use helper to add fields to updateData if they are not nil
	addField := func(key string, value *string) {
		if value != nil {
			updateData[key] = *value
		}
	}

	addField("nm", req.Name)

	if req.Phone != nil {
		if !common.IsValidPhone(*req.Phone) {
			return ErrInvalidPhoneFormat
		}
		phoneExists, err := s.repo.ExistsByPhoneAndNotID(ctx, *req.Phone, id)
		if err != nil {
			return err
		}
		if phoneExists {
			return ErrPhoneExists
		}
		updateData["ph"] = *req.Phone
	}
	if req.Email != nil {
		if !common.IsValidEmail(*req.Email) {
			return ErrInvalidEmailFormat
		}
		emailExists, err := s.repo.ExistsByEmailAndNotID(ctx, *req.Email, id)
		if err != nil {
			return err
		}
		if emailExists {
			return ErrEmailExists
		}
		updateData["em"] = *req.Email
	}
	addField("mUid", req.MAppUID)
	addField("rem", req.Remark)

	// Only update avatar URL if it has changed
	if newAvatarURL != existingAdvertiser.AvatarURL {
		updateData["avatarUrl"] = newAvatarURL
	}

	// 4. 如果有更新，则执行更新（gomongo会自动更新_mt）
	if len(updateData) > 0 {
		// 5. 执行更新
		err = s.repo.Update(ctx, objectID.Hex(), updateData)
		if err != nil {
			// 注意：goupload已经处理了文件管理，这里不需要手动清理文件
			if errors.Is(err, mongo.ErrNoDocuments) {
				return ErrAdvertiserNotFound
			}
			return fmt.Errorf("service: 更新广告主失败: %w", err)
		}

		// 注意：goupload会自动处理旧文件的清理，这里不需要手动删除
	}

	return nil
}

// Delete a client and merge its videos to another client.
func (s *service) Delete(ctx context.Context, idToDelete, targetAdvertiserID string) error {
	// 1. Validate IDs
	sourceID, err := primitive.ObjectIDFromHex(idToDelete)
	if err != nil {
		return ErrInvalidAdvertiserID
	}
	targetID, err := primitive.ObjectIDFromHex(targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("无效的目标广告主ID: %w", err)
	}

	if sourceID == targetID {
		return ErrMergeToSelf
	}

	// 2. Check if this is the last advertiser
	count, err := s.repo.Count(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("无法统计广告主数量: %w", err)
	}
	if count <= 1 {
		return ErrCannotDeleteLast
	}

	// 3. Check if target advertiser exists
	targetExists, err := s.repo.ExistsByID(ctx, targetAdvertiserID)
	if err != nil {
		return fmt.Errorf("检查目标广告主是否存在时出错: %w", err)
	}
	if !targetExists {
		return ErrTargetAdvertiserNotFound
	}

	// 4. Get source advertiser details to delete avatar later
	sourceAdvertiser, err := s.repo.FindByID(ctx, idToDelete)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return ErrAdvertiserNotFound
		}
		return fmt.Errorf("查找要删除的广告主失败: %w", err)
	}

	// 5. Perform operations sequentially without a transaction
	// Step 5a: Re-assign videos to the target advertiser
	_, err = s.videoRepo.UpdateAdvertiser(ctx, sourceID, targetID)
	if err != nil {
		return fmt.Errorf("转移视频失败: %w", err)
	}

	// Step 5b: Delete the source advertiser
	if err := s.repo.Delete(ctx, idToDelete); err != nil {
		// If this fails, the videos are already safely migrated.
		// The operation can be safely retried by the user.
		return fmt.Errorf("删除广告主失败 (视频已转移): %w", err)
	}

	// If deletion is successful, delete the avatar file using goupload
	if sourceAdvertiser.AvatarURL != "" {
		if err := s.uploadService.DeleteClientAvatar(ctx, sourceAdvertiser.AvatarURL); err != nil {
			// Log this error but don't fail the whole operation, as the main DB changes are already committed.
			logger.Log.Warn("删除头像文件失败", logger.String("avatarUrl", sourceAdvertiser.AvatarURL), logger.Error(err))
		} else {
			logger.Log.Info("头像文件删除成功", logger.String("avatarUrl", sourceAdvertiser.AvatarURL))
		}
	}

	return nil
}
